/* pages/community-help/index.wxss */
page {
  background: #f7f8fa;
  min-height: 100vh;
}

/* 隐藏所有滚动条 */
page::-webkit-scrollbar,
::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
  display: none !important;
}

.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  background-color: #f8f9fa;
  position: relative;
}

/* 固定筛选区域 */
.filter-header {
  background: #ffffff;
  padding: 16px;
  box-shadow: 0 3px 16px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;
  transition: all 0.3s ease;
}

/* 筛选区域固定状态 */
.filter-header.fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}


/* 列表容器 */
.list-container {
  background: #f8f9fa;
  padding: 16px;
  min-height: calc(100vh - 140px);
}

/* 列表容器顶部边距（当筛选区域固定时） */
.list-container.with-fixed-filter {
  padding-top: 120px; /* 根据固定区域高度调整 */
}

/* 搜索框区域 */
.search-section {
  margin-bottom: 12px;
  background-color: transparent;
}

/* 筛选和排序区域 */
.filter-sort-section {
  display: flex;
  gap: 4px;
  padding: 0 2px;
  flex-wrap: nowrap;
}

/* 筛选项样式 */
.filter-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  padding: 5px 6px;
  border-radius: 6px;
  background: #f8f9fb;
  border: 1px solid #f0f2f5;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  flex: 1;
  min-width: 0;
  white-space: nowrap;
}

.filter-item.active {
  background: #e8f2ff;
  border-color: #3B7FFF;
  box-shadow: 0 1px 4px rgba(59, 127, 255, 0.15);
}

.filter-item:active {
  transform: scale(0.96);
}

.filter-text {
  font-size: 11px;
  color: #4e5969;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.filter-item.active .filter-text {
  color: #3B7FFF;
  font-weight: 600;
}

/* 排序项样式 */
.sort-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  padding: 5px 6px;
  border-radius: 6px;
  background: #f8f9fb;
  border: 1px solid #f0f2f5;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  flex: 1;
  min-width: 0;
}

.sort-item.active {
  background: #e8f2ff;
  border-color: #3B7FFF;
  box-shadow: 0 1px 4px rgba(59, 127, 255, 0.15);
}

.sort-item:active {
  transform: scale(0.96);
}

.sort-text {
  font-size: 11px;
  color: #4e5969;
  font-weight: 500;
  white-space: nowrap;
}

.sort-item.active .sort-text {
  color: #3B7FFF;
  font-weight: 600;
}

/* 搜索框样式优化 */

/* 自定义van-search样式 */
.search-input {
  padding: 0 !important;
  background-color: transparent !important;
}

.search-input .van-search__content {
  background-color: #f8f9fb !important;
  border-radius: 14px !important;
  height: 38px !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04) !important;
  border: 1px solid #e8eaed !important;
  transition: all 0.3s ease !important;
  position: relative !important;
}

.search-input .van-search__content:focus-within {
  border-color: #3B7FFF !important;
  background-color: #ffffff !important;
  box-shadow: 0 0 0 3px rgba(59, 127, 255, 0.1) !important;
  transform: translateY(-1px) !important;
}

.search-input .van-search__content:hover:not(:focus-within) {
  border-color: #d0d3d9 !important;
  background-color: #ffffff !important;
}

.search-input .van-field__left-icon {
  color: #8a8e99 !important;
  margin-right: 8px !important;
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
}

.search-input .van-cell {
  padding: 0 14px !important;
  background-color: transparent !important;
  height: 38px !important;
  display: flex !important;
  align-items: center !important;
}

.search-input-field {
  color: #1f2329 !important;
  font-size: 14px !important;
  height: 38px !important;
  line-height: 38px !important;
  font-weight: 400 !important;
  display: flex !important;
  align-items: center !important;
}

.search-input .van-field__placeholder {
  color: #8a8e99 !important;
  font-size: 14px !important;
  line-height: 38px !important;
}

.search-input .van-field__input {
  height: 38px !important;
  line-height: 38px !important;
  display: flex !important;
  align-items: center !important;
}



/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background-color: transparent;
}

.loading-text {
  margin-top: 12px;
  font-size: 14px;
  color: #666666;
  text-align: center;
}

/* 主体内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background-color: transparent;
  text-align: center;
}

.empty-text {
  margin-top: 16px;
  font-size: 16px;
  color: #999999;
  font-weight: 500;
}

.empty-hint {
  margin-top: 8px;
  font-size: 14px;
  color: #cccccc;
  line-height: 1.4;
}

/* 互助列表样式 */
.help-list {
  padding: 0 0 80px 0;
  flex: 1;
}

.help-item {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 12px;
  margin: 0 0 8px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f2f5;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.help-item:active {
  transform: translateY(1px) scale(0.99);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.help-item:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  border-color: #e8eaed;
}

/* 标题前的标签区域 */
.title-tags-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.tag-separator {
  font-size: 11px;
  color: #c0c4cc;
  font-weight: 300;
  margin: 0 2px;
}

/* 分类标签样式 */
.category-tag {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  line-height: 1.2;
  white-space: nowrap;
  display: flex;
  align-items: center;
  background: rgba(102, 102, 102, 0.06);
  border: 1px solid rgba(102, 102, 102, 0.12);
}

/* 用户信息区域 - 移到右下角 */
.user-section {
  display: flex;
  align-items: center;
  gap: 6px;
}

.user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid #f0f2f5;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.user-avatar:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 1px;
  min-width: 0;
}

.user-name {
  font-size: 11px;
  font-weight: 500;
  color: #1f2329;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 60px;
}

.publish-time {
  font-size: 10px;
  color: #8a8e99;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 12px;
}

.content-left {
  flex: 1;
  min-width: 0;
}

.content-right {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.publish-type-tag {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  line-height: 1.2;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.request-tag {
  background: rgba(255, 107, 107, 0.06);
  color: #e74c3c;
  border: 1px solid rgba(255, 107, 107, 0.12);
}

.offer-tag {
  background: rgba(52, 152, 219, 0.06);
  color: #3498db;
  border: 1px solid rgba(52, 152, 219, 0.12);
}

.tag-text {
  font-size: 10px;
  font-weight: 500;
}

/* 分类信息样式 - 标题前 */
.category-text {
  font-size: 10px;
  color: #666666;
  font-weight: 500;
}

/* 标题样式 */
.help-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2329;
  line-height: 1.3;
  margin-bottom: 12px;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 内容区域样式 */
.help-content {
  font-size: 13px;
  color: #4e5969;
  line-height: 1.4;
  margin-bottom: 6px;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: pre-wrap;
}

/* 截止时间样式 */
.end-time {
  display: flex;
  align-items: center;
  gap: 3px;
  width: fit-content;
}

.end-time-text {
  font-size: 11px;
  color: #8a8e99;
  font-weight: 400;
}

/* 右侧图片容器 */
.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f5f6f7;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.image-container:active {
  transform: scale(0.96);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.image-container van-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* 底部信息栏 */
.bottom-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  margin-top: 6px;
  border-top: 1px solid #f5f6f7;
}

.stats {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 3px;
}

.stat-text {
  font-size: 11px;
  color: #8a8e99;
  font-weight: 500;
}



/* 悬浮操作按钮 */
.floating-actions {
  position: fixed;
  bottom: 80px;
  right: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  z-index: 100;
  transition: bottom 0.3s ease;
}

.fab-btn {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.fab-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border-radius: 24px;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.fab-btn:hover::before {
  opacity: 1;
}

.add-btn {
  background: linear-gradient(135deg, #3B7FFF, #1E64FF);
}

.fab-btn-hover {
  transform: scale(0.92);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
}

/* 发布选择弹框 */
.publish-dialog {
  padding: 24px 20px 20px;
  background: #ffffff;
}

.publish-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f2f5;
}

.publish-step-2 .publish-dialog-header {
  justify-content: space-between;
  position: relative;
}

.publish-step-2 .publish-dialog-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.publish-dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2329;
}

.publish-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.publish-option {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 12px;
  color: white;
  font-weight: 500;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.publish-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.publish-option:active::before {
  opacity: 1;
}

.publish-option-request {
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
  box-shadow: 0 4px 20px rgba(255, 107, 107, 0.3);
}

.publish-option-offer {
  background: linear-gradient(135deg, #4ECDC4 0%, #6ED5CE 100%);
  box-shadow: 0 4px 20px rgba(78, 205, 196, 0.3);
}

.publish-option:active {
  transform: translateY(2px) scale(0.98);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
}

.option-icon {
  font-size: 28px;
  line-height: 1;
  flex-shrink: 0;
}

.option-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  flex: 1;
}

.option-title {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.2;
}

.option-subtitle {
  font-size: 12px;
  opacity: 0.85;
  font-weight: 400;
  line-height: 1;
}
/* 分类列表样式 */
.category-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f2f5;
  transition: background-color 0.2s ease;
}

.category-item:last-child {
  border-bottom: none;
}

.category-item:active {
  background-color: #f8f9fa;
}

.category-name {
  font-size: 16px;
  color: #1f2329;
  font-weight: 400;
}





/* 响应式设计 */
@media (max-width: 375px) {
  .filter-sort-section {
    gap: 4px;
  }

  .filter-item,
  .sort-item {
    padding: 5px 6px;
    gap: 2px;
  }

  .filter-text,
  .sort-text {
    font-size: 11px;
  }

  .fab-btn {
    width: 44px;
    height: 44px;
    border-radius: 22px;
  }

  .floating-actions {
    bottom: 60px;
    right: 16px;
  }

  /* 小屏幕下的卡片优化 */
  .help-item {
    padding: 10px;
    border-radius: 10px;
    margin: 0 0 6px 0;
  }

  .title-tags-section {
    margin-bottom: 6px;
    gap: 4px;
  }

  .main-content {
    gap: 8px;
    margin-bottom: 10px;
  }

  .content-right {
    width: 70px;
    height: 70px;
  }

  .user-avatar {
    width: 20px;
    height: 20px;
  }

  .user-name {
    font-size: 10px;
    max-width: 50px;
  }

  .publish-time {
    font-size: 9px;
  }

  .help-title {
    font-size: 15px;
    margin-bottom: 8px;
  }

  .help-content {
    font-size: 12px;
    margin-bottom: 4px;
    white-space: pre-wrap;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .bottom-section {
    padding-top: 6px;
    margin-top: 4px;
  }
}
