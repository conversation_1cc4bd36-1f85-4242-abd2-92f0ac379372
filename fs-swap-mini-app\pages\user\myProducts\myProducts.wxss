/* pages/user/myProducts/myProducts.wxss */
.container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 自定义标签页样式 - 简约现代风格 */
.custom-tabs-container {
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1rpx 8rpx rgba(0, 0, 0, 0.04);
}

/* 一级标签页样式 - 轻量化设计 */
.main-tabs {
  display: flex;
  background: #fff;
  padding: 16rpx 32rpx 8rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.main-tab-item {
  flex: 1;
  text-align: center;
  padding: 12rpx 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #666;
  position: relative;
  transition: all 0.2s ease;
  border-radius: 8rpx;
}

.main-tab-item.active {
  color: #333;
  font-weight: 600;
  background: rgba(59, 127, 255, 0.05);
}

.main-tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 32rpx;
  height: 3rpx;
  background: #3B7FFF;
  border-radius: 2rpx;
}

/* 二级标签页样式 - 精简设计 */
.sub-tabs {
  display: flex;
  background: #fff;
  padding: 8rpx 32rpx 12rpx 32rpx;
}

.sub-tab-item {
  flex: 1;
  text-align: center;
  padding: 12rpx 0;
  font-size: 26rpx;
  font-weight: 400;
  color: #888;
  position: relative;
  transition: all 0.2s ease;
}

.sub-tab-item.active {
  color: #3B7FFF;
  font-weight: 500;
}

.sub-tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20rpx;
  height: 2rpx;
  background: #3B7FFF;
  border-radius: 1rpx;
}

/* 内容区域 */
.content-area {
  padding-bottom: 30rpx;
  background: #f8f9fa;
  min-height: calc(100vh - 200rpx);
}

/* 商品列表样式 */
.product-list {
  padding: 24rpx 20rpx 20rpx 20rpx;
}

.product-item {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
}

.product-item:active {
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.product-content {
  display: flex;
  flex: 1;
}

.product-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.product-image.no-image {
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-details {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-description {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
}

.product-price {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: bold;
}

/* 邻里互助特有样式 - 已移至下方新样式 */

.product-stats {
  display: flex;
  margin-top: 10rpx;
}

.product-views, .product-likes {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  margin-right: 20rpx;
}

.product-views text, .product-likes text {
  margin-left: 4rpx;
}

.product-actions {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}

.action-button {
  padding: 10rpx;
  color: #666;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
}

/* 没有更多样式 */
.no-more {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 20rpx 0;
}

/* 邻里互助列表样式 - 个人发布列表优化版 */
.help-list {
  padding: 24rpx 20rpx 60rpx 20rpx;
  flex: 1;
}

.help-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin: 0 0 12rpx 0;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f2f5;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.help-item:active {
  transform: translateY(1rpx) scale(0.99);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.help-item:hover {
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);
  border-color: #e8eaed;
}

/* 标题前的标签区域 - 简化版 */
.title-tags-section {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.tag-separator {
  font-size: 20rpx;
  color: #c0c4cc;
  font-weight: 300;
  margin: 0 2rpx;
}

/* 分类标签样式 */
.category-tag {
  display: flex;
  align-items: center;
}

/* 主要内容区域 - 优化布局 */
.main-content {
  display: flex;
  gap: 16rpx;
  align-items: center;
  margin-bottom: 16rpx;
}

.content-left {
  flex: 1;
  min-width: 0;
}

.content-right {
  flex-shrink: 0;
  width: 140rpx;
  height: 140rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 发布类型标签 - 简化版 */
.publish-type-tag {
  padding: 3rpx 10rpx;
  border-radius: 6rpx;
  font-size: 18rpx;
  font-weight: 500;
  line-height: 1.2;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.request-tag {
  background: rgba(255, 107, 107, 0.1);
  color: #e74c3c;
  border: 1rpx solid rgba(255, 107, 107, 0.2);
}

.offer-tag {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
  border: 1rpx solid rgba(52, 152, 219, 0.2);
}

.tag-text {
  font-size: 18rpx;
  font-weight: 500;
}

/* 分类信息样式 - 简化版 */
.category-text {
  font-size: 18rpx;
  color: #666666;
  background: rgba(102, 102, 102, 0.08);
  padding: 3rpx 10rpx;
  border-radius: 6rpx;
  font-weight: 500;
  border: 1rpx solid rgba(102, 102, 102, 0.15);
}

/* 标题样式 - 简化版 */
.help-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2329;
  line-height: 1.3;
  margin-bottom: 16rpx;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 内容区域样式 - 简化版 */
.help-content {
  font-size: 24rpx;
  color: #4e5969;
  line-height: 1.4;
  margin-bottom: 8rpx;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: pre-wrap;
}

/* 右侧图片容器 - 简化版 */
.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f5f6f7;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.image-container:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);
}

.image-container van-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* 简化的底部信息栏 - 个人发布列表专用 */
.bottom-section-simple {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12rpx;
  margin-top: 8rpx;
  border-top: 1rpx solid #f5f6f7;
}

.left-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  flex: 1;
}

.publish-time {
  font-size: 20rpx;
  color: #8a8e99;
  font-weight: 400;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.stat-text {
  font-size: 20rpx;
  color: #8a8e99;
  font-weight: 500;
}

/* 截止时间样式 - 简化版 */
.end-time {
  display: flex;
  align-items: center;
  gap: 4rpx;
  width: fit-content;
}

.end-time-text {
  font-size: 20rpx;
  color: #8a8e99;
  font-weight: 400;
}

/* 操作按钮样式 */
.action-button {
  padding: 8rpx;
  border-radius: 6rpx;
  transition: all 0.2s ease;
}

.action-button:active {
  background-color: #f5f6f7;
  transform: scale(0.95);
}

/* 弹窗内容样式 */
.dialog-content {
  padding: 30rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}
